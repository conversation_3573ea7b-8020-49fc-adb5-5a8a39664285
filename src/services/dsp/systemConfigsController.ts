// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 添加系统配置 POST /api/systemConfigs/addConfig */
export async function addUsingPOST4(body: API.SystemConfigs[], options?: { [key: string]: any }) {
  return request<API.Result>('/api/systemConfigs/addConfig', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 批量删除系统配置 POST /api/systemConfigs/batchDel */
export async function batchDelUsingPOST2(body: number[], options?: { [key: string]: any }) {
  return request<API.Result>('/api/systemConfigs/batchDel', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除同步配置 DELETE /api/systemConfigs/delConfig/${param0} */
export async function delConfigUsingDELETE4(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.delConfigUsingDELETE4Params,
  options?: { [key: string]: any },
) {
  const { id: param0, ...queryParams } = params;
  return request<API.Result>(`/api/systemConfigs/delConfig/${param0}`, {
    method: 'DELETE',
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** 修改系统配置 PUT /api/systemConfigs/editConfig */
export async function modifyConfigUsingPUT2(
  body: API.SystemConfigs[],
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/systemConfigs/editConfig', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取所有同步配置 GET /api/systemConfigs/getAll */
export async function getAllUsingGET9(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getAllUsingGET9Params,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/systemConfigs/getAll', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取所有系统配置字典 GET /api/systemConfigs/getSystemConfigsEnum */
export async function getSystemConfigsEnumUsingGET(options?: { [key: string]: any }) {
  return request<API.Result>('/api/systemConfigs/getSystemConfigsEnum', {
    method: 'GET',
    ...(options || {}),
  });
}
