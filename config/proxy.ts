/**
 * @name 代理的配置
 * @see 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 *
 * @doc https://umijs.org/docs/guides/proxy
 */

const env = process.env.NODE_ENV;

export default {
  // 如果需要自定义本地开发服务器  请取消注释按需调
  dev: {
    '/api': {
      // 要代理的地址
      // target: 'http://192.168.100.102:30555',
      target: 'http://192.168.0.139:8888',
      // target: 'http://192.168.0.148:9999',
      // 配置了这个可以从 http 代理到 https
      // 依赖 origin 的功能可能需要这个，比如 cookie
      pathRewrite: {
        //路径重写
        // '^/api': '', //选择忽略拦截器里面的单词
      },
      changeOrigin: true,
    },
  },
  /**
   * @name 详细的代理配置
   * @doc https://github.com/chimurai/http-proxy-middleware
   */
  test: {
    '/api/': {
      target: 'http://192.168.100.16:18080/',
      changeOrigin: true,
    },
  },
  pre: {
    '/api/': {
      target: 'http://192.168.100.16:18080/',
      changeOrigin: true,
    },
  },
};
